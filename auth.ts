import NextAuth from "next-auth";
import { authConfig } from "./auth.config";
import Credentials from "next-auth/providers/credentials";
import { loginAPI } from "@/networks/auth/login";
import { AuthLoginBodyI, AuthLoginResultI } from "@/networks/auth/types";

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Credentials({
      async authorize(credentials) {
        try {
          const { type, email, password, externalToken } = credentials as Omit<AuthLoginBodyI, "deviceToken"> & { externalToken: string };

          const loginResponse = await loginAPI({
            type,
            email,
            password,
            externalToken,
          });

          if (loginResponse?.token) {
            // Return the full response from your backend
            // This object will be passed to the `jwt` callback
            return loginResponse as any;
          }

          return null;
        } catch (error) {
          console.error("Authorization Error:", error);
          // Returning null will cause the signIn() promise to reject.
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // The `user` object is the one returned from the `authorize` callback.
      if (user) {
        const backendUser = user as unknown as AuthLoginResultI;
        token.id = backendUser.profileId;
        token.name = backendUser.name;
        token.email = backendUser.email;
        token.picture = backendUser.avatar;
        // Persist the tokens from your backend in the JWT
        token.accessToken = backendUser.token;
        token.jwtToken = backendUser.jwtToken;
        token.isUsernameSaved = backendUser.isUsernameSaved;
        token.isPersonalDetailsSaved = backendUser.isPersonalDetailsSaved;
        token.isWorkDetailsSaved = backendUser.isWorkDetailsSaved;
        token.isPrivacyPolicyAccepted = backendUser.isPrivacyPolicyAccepted;
      }
      return token;
    },
    async session({ session, token }) {
      // Pass the data from the JWT to the client-side session
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string;
        // Expose the tokens to the client
        session.accessToken = token.accessToken as string;
        session.jwtToken = token.jwtToken as string;
        session.isUsernameSaved = token.isUsernameSaved as boolean;
        session.isPersonalDetailsSaved = token.isPersonalDetailsSaved as boolean;
        session.isWorkDetailsSaved = token.isWorkDetailsSaved as boolean;
        session.isPrivacyPolicyAccepted = token.isPrivacyPolicyAccepted as boolean;
      }
      return session;
    },
  },
});