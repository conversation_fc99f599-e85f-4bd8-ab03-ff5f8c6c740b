import NextAuth from 'next-auth';
import { authConfig } from './auth.config';
import Credentials from 'next-auth/providers/credentials';
import Google from 'next-auth/providers/google';
import { loginAPI } from '@/networks/auth/login';
import { AuthLoginBodyI, AuthLoginResultI } from '@/networks/auth/types';

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      profile(profile, tokens) {
        try {
          // For Google OAuth, we'll handle the backend call in the signIn callback
          return {
            id: profile.sub,
            name: profile.name,
            email: profile.email,
            image: profile.picture,
            accessToken: tokens.access_token,
          };
        } catch (error) {
          console.error('Google OAuth Profile Error:', error);
          throw error;
        }
      },
    }),
    Credentials({
      async authorize(credentials) {
        try {
          const { type, email, password, externalToken } = credentials as Omit<
            AuthLoginBodyI,
            'deviceToken'
          > & { externalToken: string };

          const loginResponse = await loginAPI({
            type,
            email,
            password,
            externalToken,
          });

          if (loginResponse?.token) {
            // Return the full response from your backend
            // This object will be passed to the `jwt` callback
            return loginResponse as any;
          }

          return null;
        } catch (error) {
          console.error('Authorization Error:', error);
          // Returning null will cause the signIn() promise to reject.
          return null;
        }
      },
    }),
  ],
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === 'google') {
        try {
          // Call your backend API with the Google access token
          const loginResponse = await loginAPI({
            type: 'GOOGLE',
            externalToken: (user as any).accessToken || account.access_token!,
          });

          if (loginResponse?.token) {
            // Store the backend response in the user object for the JWT callback
            (user as any).backendData = loginResponse;
            return true;
          }
          return false;
        } catch (error) {
          console.error('Google OAuth Backend Error:', error);
          return false;
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      // Handle Google OAuth
      if (account?.provider === 'google' && user && (user as any).backendData) {
        const backendUser = (user as any).backendData as AuthLoginResultI;
        token.id = backendUser.profileId;
        token.name = backendUser.name;
        token.email = backendUser.email;
        token.picture = backendUser.avatar;
        token.accessToken = backendUser.token;
        token.jwtToken = backendUser.jwtToken;
        token.isUsernameSaved = backendUser.isUsernameSaved;
        token.isPersonalDetailsSaved = backendUser.isPersonalDetailsSaved;
        token.isWorkDetailsSaved = backendUser.isWorkDetailsSaved;
        token.isPrivacyPolicyAccepted = backendUser.isPrivacyPolicyAccepted;
      }
      // Handle credentials sign-in
      else if (user && !(user as any).backendData) {
        const backendUser = user as unknown as AuthLoginResultI;
        token.id = backendUser.profileId;
        token.name = backendUser.name;
        token.email = backendUser.email;
        token.picture = backendUser.avatar;
        token.accessToken = backendUser.token;
        token.jwtToken = backendUser.jwtToken;
        token.isUsernameSaved = backendUser.isUsernameSaved;
        token.isPersonalDetailsSaved = backendUser.isPersonalDetailsSaved;
        token.isWorkDetailsSaved = backendUser.isWorkDetailsSaved;
        token.isPrivacyPolicyAccepted = backendUser.isPrivacyPolicyAccepted;
      }
      return token;
    },
    async session({ session, token }) {
      // Pass the data from the JWT to the client-side session
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.image = token.picture as string;
        // Expose the tokens to the client
        session.accessToken = token.accessToken as string;
        session.jwtToken = token.jwtToken as string;
        session.isUsernameSaved = token.isUsernameSaved as boolean;
        session.isPersonalDetailsSaved =
          token.isPersonalDetailsSaved as boolean;
        session.isWorkDetailsSaved = token.isWorkDetailsSaved as boolean;
        session.isPrivacyPolicyAccepted =
          token.isPrivacyPolicyAccepted as boolean;
      }
      return session;
    },
  },
});
