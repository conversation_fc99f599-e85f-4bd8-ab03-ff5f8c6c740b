'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { fetchProfileAPI } from '@/networks/user/profile';
import { FetchProfileResultI } from '@/networks/user/types';

const ProfilePage = () => {
  const { data: session, status } = useSession();
  const [profile, setProfile] = useState<FetchProfileResultI | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchProfile = async () => {
      if (session?.user?.id) {
        try {
          const profileData = await fetchProfileAPI(session.user.id);
          setProfile(profileData);
        } catch (error) {
          console.error('Failed to fetch profile:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    if (status === 'authenticated') {
      fetchProfile();
    }
  }, [session, status]);

  if (status === 'loading' || loading) {
    return <div>Loading...</div>;
  }

  if (!session) {
    return <div>Please log in to view your profile.</div>;
  }

  return (
    <div>
      <h1>Profile</h1>
      {profile ? (
        <div>
          <p><strong>Name:</strong> {profile.name}</p>
          <p><strong>Email:</strong> {profile.email}</p>
          <p><strong>Username:</strong> {profile.username}</p>
          <p><strong>Designation:</strong> {profile.designationText}</p>
          <p><strong>Entity:</strong> {profile.entityText}</p>
          {profile.avatar && <img src={profile.avatar} alt="Avatar" />}
        </div>
      ) : (
        <p>Could not load profile data.</p>
      )}
    </div>
  );
};

export default ProfilePage;
