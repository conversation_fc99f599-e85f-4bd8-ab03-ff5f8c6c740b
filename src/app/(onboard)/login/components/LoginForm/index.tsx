'use client';

import Link from 'next/link';
import { Button, Input } from '@/components';
import useLoginForm from './useHook';
import EyeIcon from '@assets/svg/Eye';
import EyeClosedIcon from '@assets/svg/EyeClosed';

const LoginForm = () => {
  const {
    formData,
    errors,
    isLoading,
    handleInputChange,
    handleSubmit,
    handleGoogleSignIn,
    showPassword,
    togglePasswordVisibility,
  } = useLoginForm();

  return (
    <div className="space-y-4">
      {errors.general && (
        <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
          {errors.general}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-2">
        <div className="mb-5">
          <Input
            id="email"
            name="email"
            type="text"
            autoComplete="username"
            required
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Email or phone"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>
      <div>
        <div className="relative">
          <Input
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            autoComplete="current-password"
            required
            value={formData.password}
            onChange={handleInputChange}
            placeholder="Password"
          />

          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={togglePasswordVisibility}
          >
            {showPassword ? (
              <EyeClosedIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            ) : (
              <EyeIcon
                className="text-gray-400 hover:text-gray-600"
                width={20}
                height={20}
              />
            )}
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-sm text-red-600">{errors.password}</p>
        )}
      </div>

      <div className="text-left">
        <Link
          href="/forgot-password"
          className="text-primary text-sm font-semibold hover:underline"
        >
          Forgot password?
        </Link>
      </div>

      <div className="flex items-center mb-4">
        <input
          id="remember-me"
          name="remember-me"
          type="checkbox"
          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded accent-primary"
        />
        <label
          htmlFor="remember-me"
          className="ml-2 block text-sm text-gray-900"
        >
          Keep me logged in
        </label>
      </div>

      <div className="w-full">
        <Button
          className="w-full rounded-full text-white text-base font-semibold"
          type="submit"
          disabled={isLoading}
        >
          {isLoading ? 'Signing in...' : 'Sign in'}
        </Button>
      </div>
    </form>

    <div className="relative">
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-gray-300" />
      </div>
      <div className="relative flex justify-center text-sm">
        <span className="px-2 bg-white text-gray-500">Or continue with</span>
      </div>
    </div>

    <Button
      type="button"
      onClick={handleGoogleSignIn}
      disabled={isLoading}
      className="w-full rounded-full border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 font-semibold"
    >
      <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
        <path
          fill="currentColor"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <path
          fill="currentColor"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <path
          fill="currentColor"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <path
          fill="currentColor"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </svg>
      {isLoading ? 'Signing in...' : 'Continue with Google'}
    </Button>
  </div>
);
};
export default LoginForm;
