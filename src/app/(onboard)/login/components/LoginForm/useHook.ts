'use client';

import { useState, useCallback } from 'react';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { CredentialResponse } from '@react-oauth/google';
import { FormDataI, FormErrorsI } from './types';

const useLoginForm = () => {
  const router = useRouter();
  const [formData, setFormData] = useState<FormDataI>({
    email: '',
    password: '',
  });

  const [errors, setErrors] = useState<FormErrorsI>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData(prev => ({ ...prev, [name]: value }));

      if (errors[name as keyof FormErrorsI]) {
        setErrors(prev => ({ ...prev, [name]: undefined }));
      }
    },
    [errors]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrorsI = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Please enter your email or phone number';
    }

    if (!formData.password) {
      newErrors.password = 'Please enter your password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        return;
      }

      setIsLoading(true);
      setErrors({});

      try {
        const result = await signIn('credentials', {
          type: 'EMAIL_PASSWORD',
          email: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (result?.error) {
          setErrors({
            general: 'Invalid email or password. Please try again.',
          });
        } else {
          router.push('/forums');
        }
      } catch (err) {
        console.log(err);
        setErrors({ general: 'An error occurred. Please try again.' });
      } finally {
        setIsLoading(false);
      }
    },
    [formData, validateForm, router]
  );

  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const onGoogleLoginSuccess = useCallback(
    async (credentialResponse: CredentialResponse) => {
      setIsLoading(true);
      setErrors({});
      try {
        if (!credentialResponse.credential) {
          throw new Error('Credential not found');
        }
        const result = await signIn('credentials', {
          type: 'GOOGLE',
          externalToken: credentialResponse.credential,
          redirect: false,
        });

        if (result?.error) {
          setErrors({ general: 'Google sign-in failed. Please try again.' });
        } else {
          router.push('/forums');
        }
      } catch (err) {
        console.log(err);
        setErrors({ general: 'An error occurred during Google sign-in.' });
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  const handleAppleLogin = useCallback(async () => {
    try {
      const response = await (window as any).AppleID.auth.signIn();
      if (response?.authorization?.id_token) {
        const result = await signIn('credentials', {
          type: 'APPLE',
          externalToken: response.authorization.id_token,
          redirect: false,
        });

        if (result?.error) {
          setErrors({ general: 'Apple sign-in failed. Please try again.' });
        } else {
          router.push('/forums');
        }
      }
    } catch (error) {
      console.log(error);
      setErrors({ general: 'An error occurred during Apple sign-in.' });
    }
  }, [router]);

  return {
    formData,
    errors,
    isLoading,
    showPassword,
    handleInputChange,
    handleSubmit,
    onGoogleLoginSuccess,
    handleAppleLogin,
    togglePasswordVisibility,
  };
};

export default useLoginForm;
