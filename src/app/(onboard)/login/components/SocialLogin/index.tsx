'use client';

import AppleIcon from '@assets/svg/apple';
import useLoginForm from '../LoginForm/useHook';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';

const SocialLogin = () => {
  const { onGoogleLoginSuccess, handleAppleLogin } = useLoginForm();

  return (
    <GoogleOAuthProvider clientId={process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!}>
      <div className="space-y-4">
        <div className="google-login-button-container">
          <GoogleLogin
            onSuccess={onGoogleLoginSuccess}
            onError={() => {
              console.log('Login Failed');
            }}
            shape="pill"
            size="medium"
            logo_alignment="center"
            text="continue_with"
            width="100%"
          />
        </div>
        <button
          type="button"
          onClick={handleAppleLogin}
          className="w-full flex justify-center items-center py-1 border border-gray-400 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
        >
          <AppleIcon />
          Sign in with Apple
        </button>
      </div>
    </GoogleOAuthProvider>
  );
};

export default SocialLogin;
