import NextAuth from "next-auth";
import { authConfig } from "../../auth.config";
import Credentials from "next-auth/providers/credentials";
import z from "zod";
import { EmailSchema, PasswordSchema } from "./validation";
import { loginAPI } from "@/networks/auth/login";

export const { signIn, auth, signOut } = NextAuth({
    ...authConfig,
    providers: [
        Credentials({
            async authorize(credentials) {
                try {
                    const parsedCredentials = z
                        .object({ email: EmailSchema, password: PasswordSchema })
                        .safeParse(credentials);

                    if (!parsedCredentials.success) {
                        throw new Error("Invalid credential format")
                    }
                    const { email, password } = parsedCredentials.data;

                    const loginResponse = await loginAPI({
                        type: "EMAIL_PASSWORD",
                        email,
                        password,
                    })

                    return {
                        id: loginResponse.profileId,
                        email: loginResponse.email,
                        name: loginResponse.name,
                        jwtToken: loginResponse.jwtToken,
                        isEmailVerified: loginResponse.isEmailVerified,
                    }
                } catch (error) {
                    console.log(error, 'Error')
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async signIn({ user, account }) {
            if (account && account.provider === 'google') {
                try {
                    const response = await loginAPI({
                        type: "GOOGLE",
                        externalToken: account.id_token,
                    })
                    user.name = response.name;
                    user.email = response.email;
                    user.image = response.avatar;
                    user.id = response.profileId;

                    return true;
                } catch (err) {
                    console.log(err, 'error')
                    return false;
                }
            }
            return true;
        },
        async jwt({ token, user }) {
            if (user) {
                token.email = user.email;
                token.name = user.name
                token.picture = user.image;
            }

            return token;
        },

        async session({ session, token }) {
            session.user.id = token.id as string;
            session.user.email = token.email as string;
            session.user.name = token.name as string;
            session.user.image = token.picture as string;
            return session;
        },
    }
})