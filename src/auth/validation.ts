import { SemverR } from "@/common/validations/regex";
import z from "zod";

export const VersionNoSchema = z.string().regex(SemverR, 'x-version-no is invalid');
export const EmailSchema = z.email();
export const UUIDSchema = z.uuid();


export const AuthExtendedSchema = z.object({
    ip: z.ipv4(),
    versionNo: VersionNoSchema,
    deviceId: UUIDSchema,
    platform: z.enum(['android', 'ios', 'web_app']),
});


export const PasswordSchema = z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(32, 'Password can be at most 32 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[@$!%*?&^#]/, 'Password must contain at least one special character (@, $, !, %, *, ?, &, ^, #)');