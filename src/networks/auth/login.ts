import { apiCall } from "@/lib/api";
import { AuthLoginBodyI, AuthLoginResultI } from "./types";


export const loginAPI = async (payload: AuthLoginBodyI): Promise<AuthLoginResultI> => {
    const result = await apiCall<AuthLoginBodyI, AuthLoginResultI>(
        '/backend/api/v1/auth/login',
        'POST',
        {
            isAuth: false,
            payload,
        },
    );

    return result;
};
