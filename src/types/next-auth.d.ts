import 'next-auth';

declare module 'next-auth' {
  interface Session {
    accessToken: string;
    jwtToken: string;
    isUsernameSaved: boolean;
    isPersonalDetailsSaved: boolean;
    isWorkDetailsSaved: boolean;
    isPrivacyPolicyAccepted: boolean;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken: string;
    jwtToken: string;
    isUsernameSaved: boolean;
    isPersonalDetailsSaved: boolean;
    isWorkDetailsSaved: boolean;
    isPrivacyPolicyAccepted: boolean;
  }
}